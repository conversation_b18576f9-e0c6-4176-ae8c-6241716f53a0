import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export type MediaType = 'image';

@Entity('media')
export class Media {
  @ApiProperty({
    description: 'Unique identifier for the media file',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Original filename',
    example: 'vacation_photo.jpg',
  })
  @Column()
  original_filename: string;

  @ApiProperty({
    description: 'Stored filename on server',
    example: '123e4567-e89b-12d3-a456-************.jpg',
  })
  @Column()
  filename: string;

  @ApiProperty({
    description: 'MIME type of the file',
    example: 'image/jpeg',
  })
  @Column()
  mime_type: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 2048576,
  })
  @Column('bigint')
  file_size: number;

  @ApiProperty({
    description: 'Type of media',
    example: 'image',
    enum: ['image', 'video', 'audio', 'document'],
  })
  @Column()
  media_type: MediaType;

  @ApiProperty({
    description: 'File path on server',
    example: '/uploads/media/123e4567-e89b-12d3-a456-************.jpg',
  })
  @Column()
  file_path: string;

  @ApiProperty({
    description: 'Username of the uploader',
    example: 'johndoe',
  })
  @Column()
  uploaded_by: string;

  @ApiProperty({
    description: 'Width of image/video (if applicable)',
    example: 1920,
    nullable: true,
  })
  @Column({ type: 'integer', nullable: true })
  width: number | null;

  @ApiProperty({
    description: 'Height of image/video (if applicable)',
    example: 1080,
    nullable: true,
  })
  @Column({ type: 'integer', nullable: true })
  height: number | null;

  @ApiProperty({
    description: 'Duration of video/audio in seconds (if applicable)',
    example: 120.5,
    nullable: true,
  })
  @Column({ type: 'float', nullable: true })
  duration: number | null;

  @ApiProperty({
    description: 'Thumbnail file path for videos',
    example: '/uploads/thumbnails/123e4567-e89b-12d3-a456-************.jpg',
    nullable: true,
  })
  @Column({ type: 'text', nullable: true })
  thumbnail_path: string | null;

  @ApiProperty({
    description: 'Timestamp when the media was uploaded',
    example: '2023-10-15T14:30:00Z',
  })
  @CreateDateColumn()
  uploaded_at: Date;
}

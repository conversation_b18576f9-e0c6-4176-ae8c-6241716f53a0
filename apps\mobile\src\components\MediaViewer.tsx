import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';

import RNFS from 'react-native-fs';
import { useTheme } from '../theme';

interface ImageViewerProps {
  visible: boolean;
  imageUrl: string;
  imageFilename: string;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ImageViewer: React.FC<ImageViewerProps> = ({
  visible,
  imageUrl,
  imageFilename,
  onClose,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [isLoading, setIsLoading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  const handleDownload = async () => {
    try {
      setIsLoading(true);
      setDownloadProgress(0);

      const downloadDest = `${RNFS.DownloadDirectoryPath}/${imageFilename}`;

      const downloadOptions = {
        fromUrl: imageUrl,
        toFile: downloadDest,
        progress: (res: any) => {
          const progress = (res.bytesWritten / res.contentLength) * 100;
          setDownloadProgress(progress);
        },
      };

      const result = await RNFS.downloadFile(downloadOptions).promise;

      if (result.statusCode === 200) {
        Alert.alert('Success', `File saved to Downloads folder`);
      } else {
        Alert.alert('Error', 'Failed to download file');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to download file');
    } finally {
      setIsLoading(false);
      setDownloadProgress(0);
    }
  };

  const renderImage = () => {
    return (
      <Image
        source={{ uri: imageUrl }}
        style={styles.image}
        resizeMode="contain"
      />
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.filename} numberOfLines={1}>
            {imageFilename}
          </Text>
          <TouchableOpacity
            style={styles.downloadButton}
            onPress={handleDownload}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Text style={styles.downloadText}>⬇</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Image Content */}
        <View style={styles.imageContainer}>
          {renderImage()}
        </View>

        {/* Download Progress */}
        {isLoading && downloadProgress > 0 && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${downloadProgress}%` },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {Math.round(downloadProgress)}%
            </Text>
          </View>
        )}
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    color: colors.white,
    fontSize: 20,
    fontWeight: 'bold',
  },
  filename: {
    flex: 1,
    color: colors.white,
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  downloadButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  downloadText: {
    color: colors.white,
    fontSize: 20,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: screenWidth,
    height: screenHeight - 150,
  },

  progressContainer: {
    position: 'absolute',
    bottom: 50,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: colors.gray600,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  progressText: {
    color: colors.white,
    fontSize: 12,
  },
});

export default ImageViewer;

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Text,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { sendMessageRequest, selectConnected, selectError } from '../redux/slices/socketSlice';
import { selectEmojiReactionsEnabled } from '../redux/slices/emojiReactionSlice';
import EmojiBar from './EmojiBar';
import MediaPicker, { MediaFile } from './MediaPicker';
import { useTheme, radius } from '../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';


interface MessageInputProps {
  receiverUsername: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  receiverUsername,
  onFocus,
  onBlur
}) => {
  const dispatch = useAppDispatch();
  const connected = useAppSelector(selectConnected);
  const error = useAppSelector(selectError);
  const emojiReactionsEnabled = useAppSelector(selectEmojiReactionsEnabled);
  const { colors } = useTheme();

  const [message, setMessage] = useState<string>('');
  const [sendStatus, setSendStatus] = useState<{ success: boolean, message: string } | null>(null);
  const [showMediaPicker, setShowMediaPicker] = useState(false);
  const [isUploadingMedia, setIsUploadingMedia] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // Watch for errors from Redux
  useEffect(() => {
    if (error && error.includes('Failed to send message')) {
      setSendStatus({
        success: false,
        message: error
      });
    }
  }, [error]);

  // Clear send status after 3 seconds
  useEffect(() => {
    if (sendStatus) {
      const timer = setTimeout(() => {
        setSendStatus(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sendStatus]);

  const handleSubmit = () => {
    if (!message.trim() || !receiverUsername || !connected) {
      if (!connected) {
        setSendStatus({
          success: false,
          message: 'Not connected to server'
        });
      }
      return;
    }

    // Send as a text message type
    dispatch(sendMessageRequest({
      receiverUsername,
      messageText: message.trim(),
      messageType: 'text'
    }));
    setMessage('');

    // Focus back on the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleMediaSelected = async (mediaFile: MediaFile) => {
    if (!receiverUsername || !connected) {
      Alert.alert('Error', 'Cannot send media: not connected');
      return;
    }

    try {
      setIsUploadingMedia(true);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', {
        uri: mediaFile.uri,
        type: mediaFile.type,
        name: mediaFile.name,
      } as any);

      // Upload to backend
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3002'}/api/media/upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'multipart/form-data',
          // Add auth header if needed
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload media');
      }

      const uploadedMedia = await response.json();

      // Send media message via socket
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: message.trim() || '', // Optional caption
        messageType: 'media',
        mediaId: uploadedMedia.id,
        mediaUrl: `/api/media/${uploadedMedia.id}`,
        mediaType: uploadedMedia.media_type,
        mediaFilename: uploadedMedia.original_filename,
        mediaFileSize: uploadedMedia.file_size,
      }));

      setMessage(''); // Clear caption
      setSendStatus({
        success: true,
        message: 'Media sent successfully'
      });

    } catch (error) {
      console.error('Media upload error:', error);
      setSendStatus({
        success: false,
        message: 'Failed to send media'
      });
    } finally {
      setIsUploadingMedia(false);
    }
  };

  // Typing indicator state
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Send typing indicator
  const sendTypingIndicator = (typing: boolean) => {
    if (connected && receiverUsername) {
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: typing ? 'typing' : 'stopped_typing',
        messageType: 'typing'
      }));
    }
  };

  // Handle text input change and typing indicator
  const handleTextChange = (text: string) => {
    setMessage(text);

    // Handle typing indicator
    if (text.trim() && !isTyping) {
      // User started typing
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!text.trim() && isTyping) {
      // User stopped typing
      setIsTyping(false);
      sendTypingIndicator(false);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop the typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(false);
      }
    }, 3000);
  };

  // Clean up typing indicator on unmount
  useEffect(() => {
    return () => {
      // Clear typing timeout and send stopped typing on unmount
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (isTyping && connected && receiverUsername) {
        sendTypingIndicator(false);
      }
    };
  }, [isTyping, connected, receiverUsername]);

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {
    // Currently empty in frontend, could be used for inserting emoji into text
  };

  // Handle emoji long press
  const handleEmojiLongPress = (emoji: string) => {
    if (connected && receiverUsername) {
      // Get the mood name from the emoji
      let mood = '';
      switch (emoji) {
        case '😊': mood = 'happy'; break;
        case '😂': mood = 'laughing'; break;
        case '😡': mood = 'angry'; break;
        case '😢': mood = 'sad'; break;
        case '❤️': mood = 'love'; break;
        case '👍': mood = 'thumbsUp'; break;
        default: mood = 'feeling';
      }

      // Send emoji reaction message with emoji and mood
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: `${emoji}:${mood}`,
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle emoji release
  const handleEmojiRelease = () => {
    if (connected && receiverUsername) {
      // Send message to stop showing emoji reaction
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'stopped_reaction',
        messageType: 'emoji_reaction'
      }));
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      {sendStatus && (
        <View style={[
          styles.sendStatus,
          sendStatus.success ? styles.successStatus : styles.errorStatus
        ]}>
          <Text style={styles.statusText}>{sendStatus.message}</Text>
        </View>
      )}

      {/* Emoji Bar */}
      {emojiReactionsEnabled && (
        <EmojiBar
          onEmojiClick={handleEmojiClick}
          onEmojiLongPress={handleEmojiLongPress}
          onEmojiRelease={handleEmojiRelease}
        />
      )}

      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={[
            styles.attachButton,
            (!connected || !receiverUsername) && styles.disabledButton
          ]}
          onPress={() => setShowMediaPicker(true)}
          disabled={!connected || !receiverUsername || isUploadingMedia}
        >
          {isUploadingMedia ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <Icon name="attach-file" style={styles.attachButtonText} />
          )}
        </TouchableOpacity>

        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={message}
          onChangeText={handleTextChange}
          placeholder={connected ? 'Type a message...' : 'Connect to send messages'}
          editable={connected && !!receiverUsername}
          multiline
          onFocus={onFocus}
          onBlur={onBlur}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
        />

        <TouchableOpacity
          style={[
            styles.sendButton,
            (!connected || !message.trim() || !receiverUsername) && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!connected || !message.trim() || !receiverUsername}
        >
          <Icon name="arrow-back" style={[styles.sendButtonText, { transform: [{ rotate: '135deg' }] }]} />
        </TouchableOpacity>
      </View>

      {/* Media Picker Modal */}
      <Modal
        visible={showMediaPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMediaPicker(false)}
      >
        <MediaPicker
          onMediaSelected={handleMediaSelected}
          onClose={() => setShowMediaPicker(false)}
        />
      </Modal>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
  },
  sendStatus: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  successStatus: {
    backgroundColor: colors.success,
  },
  errorStatus: {
    backgroundColor: colors.danger,
  },
  statusText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  attachButton: {
    backgroundColor: colors.gray400,
    borderRadius: radius.round,
    paddingHorizontal: 10,
    paddingVertical: 10,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  attachButtonText: {
    color: colors.white,
    fontSize: 20,
    fontFamily: 'Outfit-Medium',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: radius.round,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    color: colors.text,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: radius.round,
    paddingHorizontal: 10,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: colors.gray300,
  },
  sendButtonText: {
    color: colors.white,
    fontSize: 24,
    fontFamily: 'Outfit-Medium',
  },
});

export default MessageInput;

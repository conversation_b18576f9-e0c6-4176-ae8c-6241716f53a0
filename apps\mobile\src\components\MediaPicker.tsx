import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { launchImageLibrary, launchCamera, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { useTheme } from '../theme';

interface MediaPickerProps {
  onImageSelected: (image: ImageFile) => void;
  onClose: () => void;
}

export interface ImageFile {
  uri: string;
  type: string;
  name: string;
  size: number;
  width?: number;
  height?: number;
}

const MediaPicker: React.FC<MediaPickerProps> = ({ onImageSelected, onClose }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [isLoading, setIsLoading] = useState(false);

  const checkPermissions = async (type: 'camera' | 'library'): Promise<boolean> => {
    const permission = Platform.OS === 'ios'
      ? (type === 'camera' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.IOS.PHOTO_LIBRARY)
      : (type === 'camera' ? PERMISSIONS.ANDROID.CAMERA : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE);

    const result = await check(permission);

    if (result === RESULTS.GRANTED) {
      return true;
    }

    if (result === RESULTS.DENIED) {
      const requestResult = await request(permission);
      return requestResult === RESULTS.GRANTED;
    }

    return false;
  };

  const handleImagePicker = async (source: 'camera' | 'library') => {
    try {
      setIsLoading(true);

      const hasPermission = await checkPermissions(source);
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Please grant permission to access your camera/photos');
        return;
      }

      const options = {
        mediaType: 'photo' as MediaType,
        includeBase64: false,
        maxHeight: 2000,
        maxWidth: 2000,
        quality: 0.8,
      };

      const callback = (response: ImagePickerResponse) => {
        setIsLoading(false);

        if (response.didCancel || response.errorMessage) {
          return;
        }

        if (response.assets && response.assets[0]) {
          const asset = response.assets[0];

          if (!asset.uri || !asset.type || !asset.fileName) {
            Alert.alert('Error', 'Invalid file selected');
            return;
          }

          // Check file size (10MB limit)
          if (asset.fileSize && asset.fileSize > 10 * 1024 * 1024) {
            Alert.alert('File Too Large', 'Please select a file smaller than 10MB');
            return;
          }

          const imageFile: ImageFile = {
            uri: asset.uri,
            type: asset.type,
            name: asset.fileName,
            size: asset.fileSize || 0,
            width: asset.width,
            height: asset.height,
          };

          onImageSelected(imageFile);
          onClose();
        }
      };

      if (source === 'camera') {
        launchCamera(options, callback);
      } else {
        launchImageLibrary(options, callback);
      }
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Error', 'Failed to pick media');
    }
  };



  return (
    <View style={styles.container}>
      <View style={styles.modal}>
        <Text style={styles.title}>Select Image</Text>

        <TouchableOpacity
          style={styles.option}
          onPress={() => handleImagePicker('camera')}
          disabled={isLoading}
        >
          <Text style={styles.optionText}>📷 Take Photo</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.option}
          onPress={() => handleImagePicker('library')}
          disabled={isLoading}
        >
          <Text style={styles.optionText}>🖼️ Choose from Gallery</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onClose}
          disabled={isLoading}
        >
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 300,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  option: {
    backgroundColor: colors.primaryLight,
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    color: colors.text,
    fontWeight: '500',
  },
  cancelButton: {
    backgroundColor: colors.gray200,
    padding: 15,
    borderRadius: 8,
    marginTop: 10,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: '500',
  },
});

export default MediaPicker;

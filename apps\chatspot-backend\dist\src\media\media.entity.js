"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Media = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let Media = class Media {
    id;
    original_filename;
    filename;
    mime_type;
    file_size;
    media_type;
    file_path;
    uploaded_by;
    width;
    height;
    duration;
    thumbnail_path;
    uploaded_at;
};
exports.Media = Media;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the media file',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Media.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Original filename',
        example: 'vacation_photo.jpg',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Media.prototype, "original_filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stored filename on server',
        example: '123e4567-e89b-12d3-a456-************.jpg',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Media.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MIME type of the file',
        example: 'image/jpeg',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Media.prototype, "mime_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File size in bytes',
        example: 2048576,
    }),
    (0, typeorm_1.Column)('bigint'),
    __metadata("design:type", Number)
], Media.prototype, "file_size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of media',
        example: 'image',
        enum: ['image', 'video', 'audio', 'document'],
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Media.prototype, "media_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File path on server',
        example: '/uploads/media/123e4567-e89b-12d3-a456-************.jpg',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Media.prototype, "file_path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username of the uploader',
        example: 'johndoe',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Media.prototype, "uploaded_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Width of image/video (if applicable)',
        example: 1920,
        nullable: true,
    }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Object)
], Media.prototype, "width", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Height of image/video (if applicable)',
        example: 1080,
        nullable: true,
    }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Object)
], Media.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duration of video/audio in seconds (if applicable)',
        example: 120.5,
        nullable: true,
    }),
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Object)
], Media.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thumbnail file path for videos',
        example: '/uploads/thumbnails/123e4567-e89b-12d3-a456-************.jpg',
        nullable: true,
    }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Object)
], Media.prototype, "thumbnail_path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the media was uploaded',
        example: '2023-10-15T14:30:00Z',
    }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Media.prototype, "uploaded_at", void 0);
exports.Media = Media = __decorate([
    (0, typeorm_1.Entity)('media')
], Media);
//# sourceMappingURL=media.entity.js.map